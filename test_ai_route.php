<?php

// Test AI route directly
echo "Testing AI route...\n";

$url = 'http://localhost/admin/course/generate-ai-outline';
$data = [
    'course_description' => 'Test course description',
    'target_audience' => 'beginner',
    'course_duration' => 'short',
    'course_id' => 1,
    '_token' => 'test-token'
];

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => http_build_query($data),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/x-www-form-urlencoded',
        'X-CSRF-TOKEN: test-token'
    ],
    CURLOPT_TIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => false
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_errno($ch)) {
    echo "CURL Error: " . curl_error($ch) . "\n";
} else {
    echo "HTTP Code: $httpCode\n";
    echo "Response: " . substr($response, 0, 200) . "...\n";
}

curl_close($ch);
