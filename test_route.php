<?php

// Test route generation
echo "Testing route generation...\n";

// Simulate <PERSON><PERSON> route helper
function route($name, $params = []) {
    $routes = [
        'admin.course.generate_ai_outline' => '/admin/course/generate-ai-outline',
        'admin.course.add_ai_outline' => '/admin/course/add-ai-outline'
    ];
    
    return $routes[$name] ?? 'ROUTE_NOT_FOUND';
}

echo "Generate AI Outline Route: " . route('admin.course.generate_ai_outline') . "\n";
echo "Add AI Outline Route: " . route('admin.course.add_ai_outline') . "\n";
