<div class="ai-outline-generator">
    <!-- Step 1: Input Course Description -->
    <div id="step-1" class="step-content">
        <div class="text-center mb-4">
            <div class="ai-icon mb-3">
                <i class="fi-rr-magic-wand" style="font-size: 3rem; color: #667eea;"></i>
            </div>
            <h4 class="text-primary">Tạo Outline Khóa Học Bằng AI</h4>
            <p class="text-muted">Mô tả chi tiết về khóa học để AI tạo outline chuyên nghiệp</p>
        </div>

        <form id="ai-outline-form">
            @csrf
            <input type="hidden" name="course_id" value="{{ $id }}">

            <div class="mb-4">
                <label for="course_description" class="form-label fw-bold">
                    <i class="fi-rr-document me-2"></i><PERSON><PERSON> tả chi tiết về khóa học
                </label>
                <textarea
                    class="form-control"
                    id="course_description"
                    name="course_description"
                    rows="8"
                    placeholder="Ví dụ: <PERSON>h<PERSON><PERSON> học Shopee từ A-Z dành cho người mới bắt đầu. Bao gồm: cách tạo shop, tối ưu sản phẩm, quảng cáo, chăm sóc khách hàng, phân tích dữ liệu..."
                    required></textarea>
                <small class="text-muted">
                    <i class="fi-rr-info me-1"></i>
                    Mô tả càng chi tiết, AI sẽ tạo outline càng chính xác và phù hợp
                </small>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <label for="target_audience" class="form-label fw-bold">
                        <i class="fi-rr-users me-2"></i>Đối tượng học viên
                    </label>
                    <select class="form-select" id="target_audience" name="target_audience">
                        <option value="beginner">Người mới bắt đầu</option>
                        <option value="intermediate">Trình độ trung bình</option>
                        <option value="advanced">Nâng cao</option>
                        <option value="all">Tất cả trình độ</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="course_duration" class="form-label fw-bold">
                        <i class="fi-rr-clock me-2"></i>Thời lượng mong muốn
                    </label>
                    <select class="form-select" id="course_duration" name="course_duration">
                        <option value="short">Ngắn (5-10 bài học)</option>
                        <option value="medium">Trung bình (15-25 bài học)</option>
                        <option value="long">Dài (30+ bài học)</option>
                    </select>
                </div>
            </div>

            <div class="mb-4">
                <label for="course_goals" class="form-label fw-bold">
                    <i class="fi-rr-target me-2"></i>Mục tiêu khóa học (tùy chọn)
                </label>
                <textarea
                    class="form-control"
                    id="course_goals"
                    name="course_goals"
                    rows="3"
                    placeholder="Học viên sẽ đạt được gì sau khi hoàn thành khóa học..."></textarea>
            </div>

            <div class="d-flex justify-content-between">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fi-rr-cross me-1"></i> Hủy
                </button>
                <div>
                    <button type="button" class="btn btn-outline-info me-2" id="test-ajax-btn" onclick="testAjaxConnection();">
                        <i class="fi-rr-test-tube me-1"></i> Test AJAX
                    </button>
                    <button type="button" class="btn btn-primary" id="generate-outline-btn" onclick="handleGenerateOutline();">
                        <i class="fi-rr-magic-wand me-1"></i> Tạo Outline
                        <span class="spinner-border spinner-border-sm ms-2 d-none" role="status"></span>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Step 2: Display Generated Outline -->
    <div id="step-2" class="step-content d-none">
        <div class="text-center mb-4">
            <div class="ai-icon mb-3">
                <i class="fi-rr-check-circle" style="font-size: 3rem; color: #28a745;"></i>
            </div>
            <h4 class="text-success">Outline Đã Được Tạo!</h4>
            <p class="text-muted">Chỉnh sửa tên sections và lessons, sau đó chọn những mục muốn thêm</p>
        </div>

        <div class="outline-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="select-all">
                    <label class="form-check-label fw-bold" for="select-all">
                        Chọn tất cả
                    </label>
                </div>
                <small class="text-muted">
                    <i class="fi-rr-info me-1"></i>Click vào tên để chỉnh sửa
                </small>
            </div>

            <div id="generated-outline">
                <!-- AI generated outline will be inserted here -->
            </div>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <button type="button" class="btn btn-outline-secondary" id="back-to-step1">
                <i class="fi-rr-arrow-left me-1"></i> Quay lại
            </button>
            <div>
                <button type="button" class="btn btn-outline-primary me-2" id="regenerate-outline">
                    <i class="fi-rr-refresh me-1"></i> Tạo lại
                </button>
                <button type="button" class="btn btn-success" id="add-selected-outline">
                    <i class="fi-rr-plus me-1"></i> Thêm mục đã chọn
                    <span class="spinner-border spinner-border-sm ms-2 d-none" role="status"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.ai-outline-generator {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.ai-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.outline-section {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
}

.section-header {
    background: #667eea;
    color: white;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-title {
    flex: 1;
    background: transparent;
    border: none;
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.section-title:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 4px;
}

.section-lessons {
    padding: 0;
}

.lesson-item {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 12px;
    background: white;
    transition: background-color 0.2s;
}

.lesson-item:hover {
    background: #f8fafc;
}

.lesson-item:last-child {
    border-bottom: none;
}

.lesson-title {
    flex: 1;
    background: transparent;
    border: none;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.lesson-title:focus {
    outline: none;
    background: #eff6ff;
    border: 1px solid #3b82f6;
}

.lesson-icon {
    color: #6b7280;
    font-size: 14px;
    width: 20px;
    text-align: center;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}
</style>

<script>
// Global functions for onclick handlers
function testAjaxConnection() {
    console.log('Test AJAX function called');
    alert('Test AJAX button clicked! Check console.');

    fetch('/admin/course/test-ai', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf_token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Test response:', data);
        alert('AJAX Test thành công! Response: ' + JSON.stringify(data));
    })
    .catch(error => {
        console.error('Test error:', error);
        alert('AJAX Test thất bại: ' + error.message);
    });
}

function handleGenerateOutline() {
    console.log('Generate outline function called');

    // Validate form
    const courseDescription = document.getElementById('course_description').value.trim();
    if (!courseDescription || courseDescription.length < 10) {
        alert('Vui lòng nhập mô tả khóa học ít nhất 10 ký tự');
        return;
    }

    console.log('Validation passed! Starting outline generation...');
    generateOutlineAjax();
}

$(document).ready(function() {
    console.log('AI Outline Generator script loaded with jQuery');

    // Wait a bit for modal to be fully rendered
    setTimeout(function() {
        initAiOutlineGenerator();
    }, 500);
});

function initAiOutlineGenerator() {
    console.log('Initializing AI Outline Generator');

    const form = document.getElementById('ai-outline-form');
    const generateBtn = document.getElementById('generate-outline-btn');
    const step1 = document.getElementById('step-1');
    const step2 = document.getElementById('step-2');
    const backBtn = document.getElementById('back-to-step1');
    const regenerateBtn = document.getElementById('regenerate-outline');
    const addSelectedBtn = document.getElementById('add-selected-outline');
    const selectAllCheckbox = document.getElementById('select-all');
    const testAjaxBtn = document.getElementById('test-ajax-btn');

    console.log('Elements found:', {
        form: !!form,
        generateBtn: !!generateBtn,
        step1: !!step1,
        step2: !!step2,
        testAjaxBtn: !!testAjaxBtn
    });

    if (!generateBtn) {
        console.error('Generate button not found!');
        return;
    }

    // Test AJAX button
    if (testAjaxBtn) {
        testAjaxBtn.addEventListener('click', function() {
            console.log('Test AJAX button clicked');

            fetch('/admin/course/test-ai', {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf_token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Test response:', data);
                alert('AJAX Test thành công! Check console để xem response.');
            })
            .catch(error => {
                console.error('Test error:', error);
                alert('AJAX Test thất bại: ' + error.message);
            });
        });
    }

    // Handle button click instead of form submission
    generateBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Generate button clicked');

        // Validate form
        const courseDescription = document.getElementById('course_description').value.trim();
        if (!courseDescription || courseDescription.length < 10) {
            alert('Vui lòng nhập mô tả khóa học ít nhất 10 ký tự');
            return;
        }

        generateOutlineAjax();
    });

    // Back to step 1
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            step2.classList.add('d-none');
            step1.classList.remove('d-none');
        });
    }

    // Regenerate outline
    if (regenerateBtn) {
        regenerateBtn.addEventListener('click', function() {
            step2.classList.add('d-none');
            step1.classList.remove('d-none');
        });
    }

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('#generated-outline input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Add selected outline
    if (addSelectedBtn) {
        addSelectedBtn.addEventListener('click', function() {
            addSelectedOutline();
        });
    }
}

function generateOutlineAjax() {
    console.log('generateOutlineAjax called');

    // Create FormData manually
    const formData = new FormData();
    formData.append('course_id', '{{ $id }}');
    formData.append('course_description', document.getElementById('course_description').value);
    formData.append('target_audience', document.getElementById('target_audience').value);
    formData.append('course_duration', document.getElementById('course_duration').value);
    formData.append('course_goals', document.getElementById('course_goals').value);

    // Get button and show loading state
    const generateBtn = document.getElementById('generate-outline-btn');
    if (!generateBtn) {
        console.error('Generate button not found!');
        return;
    }

    // Show loading state
    const originalButtonText = generateBtn.innerHTML;
    generateBtn.disabled = true;
    generateBtn.innerHTML = '<i class="fi-rr-magic-wand me-1"></i> Đang tạo... <span class="spinner-border spinner-border-sm ms-2" role="status"></span>';

    // Check CSRF token
    const csrfToken = document.querySelector('meta[name="csrf_token"]');
    if (!csrfToken) {
        console.error('CSRF token not found');
        alert('Lỗi: Không tìm thấy CSRF token');
        generateBtn.disabled = false;
        generateBtn.innerHTML = originalButtonText;
        return;
    }

    console.log('CSRF token found:', csrfToken.getAttribute('content'));

    const generateUrl = '/admin/course/generate-ai-outline';
    console.log('Sending request to:', generateUrl);
    console.log('Form data:', {
        course_id: '{{ $id }}',
        course_description: document.getElementById('course_description').value,
        target_audience: document.getElementById('target_audience').value,
        course_duration: document.getElementById('course_duration').value
    });

    fetch(generateUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': csrfToken.getAttribute('content')
        }
    })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            displayOutline(data.outline);
            document.getElementById('step-1').classList.add('d-none');
            document.getElementById('step-2').classList.remove('d-none');
        } else {
            console.error('API error:', data.message);
            alert('Có lỗi xảy ra: ' + (data.message || 'Không thể tạo outline'));
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        alert('Có lỗi xảy ra khi kết nối với AI: ' + error.message);
    })
    .finally(() => {
        generateBtn.disabled = false;
        generateBtn.innerHTML = originalButtonText;
    });
}

function displayOutline(outline) {
        const container = document.getElementById('generated-outline');
        container.innerHTML = '';

        outline.forEach((section, sectionIndex) => {
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'outline-section';

            let lessonsHtml = '';
            section.lessons.forEach((lesson, lessonIndex) => {
                lessonsHtml += `
                    <div class="lesson-item">
                        <input class="form-check-input" type="checkbox" id="lesson_${sectionIndex}_${lessonIndex}" checked>
                        <i class="fi-rr-document lesson-icon"></i>
                        <input type="text" class="lesson-title" value="${lesson}" data-section="${sectionIndex}" data-lesson="${lessonIndex}">
                    </div>
                `;
            });

            sectionDiv.innerHTML = `
                <div class="section-header">
                    <input class="form-check-input" type="checkbox" id="section_${sectionIndex}" checked>
                    <i class="fi-rr-folder"></i>
                    <input type="text" class="section-title" value="${section.title}" data-section="${sectionIndex}" placeholder="Tên section...">
                </div>
                <div class="section-lessons">
                    ${lessonsHtml}
                </div>
            `;

            container.appendChild(sectionDiv);
        });
}

function addSelectedOutline() {
        const sections = [];
        const sectionCheckboxes = document.querySelectorAll('input[id^="section_"]:checked');

        sectionCheckboxes.forEach(sectionCheckbox => {
            const sectionIndex = sectionCheckbox.id.split('_')[1];
            const sectionTitle = document.querySelector(`input[data-section="${sectionIndex}"].section-title`).value;

            const lessons = [];
            const lessonCheckboxes = document.querySelectorAll(`input[id^="lesson_${sectionIndex}_"]:checked`);

            lessonCheckboxes.forEach(lessonCheckbox => {
                const lessonIndex = lessonCheckbox.id.split('_')[2];
                const lessonTitle = document.querySelector(`input[data-section="${sectionIndex}"][data-lesson="${lessonIndex}"]`).value;
                lessons.push(lessonTitle);
            });

            if (lessons.length > 0) {
                sections.push({
                    title: sectionTitle,
                    lessons: lessons
                });
            }
        });

        if (sections.length === 0) {
            alert('Vui lòng chọn ít nhất một section và lesson');
            return;
        }

        const addSelectedBtn = document.getElementById('add-selected-outline');
        if (!addSelectedBtn) {
            console.error('Add selected button not found');
            return;
        }

        const spinner = addSelectedBtn.querySelector('.spinner-border');
        addSelectedBtn.disabled = true;
        if (spinner) spinner.classList.remove('d-none');

        const formData = new FormData();
        formData.append('course_id', '{{ $id }}');
        formData.append('outline_data', JSON.stringify(sections));
        formData.append('_token', document.querySelector('meta[name="csrf_token"]').getAttribute('content'));

        const addUrl = '/admin/course/add-ai-outline';
        fetch(addUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Đã thêm outline thành công!');
                $('#ajaxModal').modal('hide');
                location.reload(); // Reload to show new curriculum
            } else {
                alert('Có lỗi xảy ra: ' + (data.message || 'Không thể thêm outline'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi thêm outline');
        })
        .finally(() => {
            addSelectedBtn.disabled = false;
            if (spinner) spinner.classList.add('d-none');
        });
}
</script>
