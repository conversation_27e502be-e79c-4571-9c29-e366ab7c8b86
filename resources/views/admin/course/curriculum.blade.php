<div class="w-100">
    <!-- Modern Curriculum Builder -->
    <div class="curriculum-builder">
        <!-- Header Actions -->
        <div class="curriculum-header mb-4">
            <div class="d-flex gap-2 align-items-center flex-wrap">
                <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.course.create_section', 'id' => $course_details->id]) }}', 'Thêm section mới')" class="btn btn-primary btn-sm">
                    <i class="fi-rr-add me-1"></i> Thêm Section
                </button>

                @if ($sections->count() > 0)
                    <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.course.lesson_type', 'id' => $course_details->id]) }}', 'Thêm bài học mới')" class="btn btn-success btn-sm">
                        <i class="fi-rr-document me-1"></i> Thêm Bài họ<PERSON>
                    </button>

                    <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.quiz.create', 'id' => $course_details->id]) }}', 'Thêm quiz mới')" class="btn btn-warning btn-sm">
                        <i class="fi-rr-quiz me-1"></i> Thêm Quiz
                    </button>
                @endif

                <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.course.copy_lessons_from_course', 'id' => $course_details->id]) }}', 'Sao chép từ khóa học khác', 'modal-lg')" class="btn btn-info btn-sm">
                    <i class="fi-rr-copy me-1"></i> Sao chép từ khóa khác
                </button>

                <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.course.ai_outline_generator', 'id' => $course_details->id]) }}', 'Tạo Outline bằng AI', 'modal-xl')" class="btn btn-gradient btn-sm">
                    <i class="fi-rr-magic-wand me-1"></i> Tạo Outline bằng AI
                </button>

                @if ($sections->count() > 0)
                    <div class="ms-auto">
                        <small class="text-muted">
                            <i class="fi-rr-info me-1"></i> Kéo thả để sắp xếp sections và lessons
                        </small>
                    </div>
                @endif
            </div>
        </div>


        <!-- Sortable Curriculum Container -->
        <div id="curriculum-container" class="curriculum-sections" data-course-id="{{ $course_details->id }}">
            @forelse ($sections as $key => $section)
                @php
                    $lessons = DB::table('lessons')
                        ->join('sections', 'lessons.section_id', 'sections.id')
                        ->select('lessons.*', 'sections.title as section_title')
                        ->where('lessons.section_id', $section->id)
                        ->orderBy('sort')
                        ->get();
                @endphp

                <!-- Section Card -->
                <div class="section-card" data-section-id="{{ $section->id }}">
                    <div class="section-header">
                        <div class="section-drag-handle">
                            <i class="fi-rr-apps-sort text-muted"></i>
                        </div>
                        <div class="section-info">
                            <h5 class="section-title mb-1">
                                <span class="section-number">{{ $key + 1 }}.</span>
                                {{ $section->title }}
                            </h5>
                            <small class="text-muted">{{ $lessons->count() }} bài học</small>
                        </div>
                        <div class="section-actions">
                            <button type="button" class="btn btn-sm btn-outline-secondary section-toggle" data-bs-toggle="tooltip" title="Thu gọn/Mở rộng">
                                <i class="fi-rr-angle-down"></i>
                            </button>
                            <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.course.section_edit', 'id' => $section->id]) }}', 'Chỉnh sửa section')" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                <i class="fi-rr-pencil"></i>
                            </button>
                            <button type="button" onclick="confirmModal('{{ route('admin.section.delete', $section->id) }}')" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Xóa section">
                                <i class="fi-rr-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Lessons Container -->
                    <div class="section-lessons" data-section-id="{{ $section->id }}">
                        @if ($lessons->count() > 0)
                            @foreach ($lessons as $lessonKey => $lesson)
                                <div class="lesson-item" data-lesson-id="{{ $lesson->id }}">
                                    <div class="lesson-drag-handle">
                                        <i class="fi-rr-apps-sort text-muted"></i>
                                    </div>

                                    <div class="lesson-icon">
                                        @if ($lesson->lesson_type == 'quiz')
                                            <i class="fi-rr-quiz text-warning"></i>
                                        @elseif ($lesson->lesson_type == 'video')
                                            <i class="fi-rr-play text-primary"></i>
                                        @elseif ($lesson->lesson_type == 'document')
                                            <i class="fi-rr-document text-info"></i>
                                        @else
                                            <i class="fi-rr-document text-secondary"></i>
                                        @endif
                                    </div>

                                    <div class="lesson-content">
                                        <h6 class="lesson-title mb-1">{{ $lesson->title }}</h6>
                                        <div class="lesson-meta">
                                            <span class="badge badge-{{ $lesson->lesson_type == 'quiz' ? 'warning' : ($lesson->lesson_type == 'video' ? 'primary' : 'info') }} me-2">
                                                {{ ucfirst($lesson->lesson_type) }}
                                            </span>
                                            @if ($lesson->is_free)
                                                <span class="badge badge-success me-2">Miễn phí</span>
                                            @endif
                                            @if ($lesson->duration && $lesson->duration != '00:00:00')
                                                <small class="text-muted">{{ $lesson->duration }}</small>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="lesson-actions">
                                        @if ($lesson->lesson_type == 'quiz')
                                            <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.quiz_result.index', 'id' => $lesson->id]) }}', 'Kết quả Quiz', 'modal-xl')" class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="Xem kết quả">
                                                <i class="fi-rr-clipboard-list-check"></i>
                                            </button>
                                            <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.questions.index', 'id' => $lesson->id]) }}', 'Câu hỏi Quiz', 'modal-lg')" class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" title="Quản lý câu hỏi">
                                                <i class="fi-rr-poll-h"></i>
                                            </button>
                                            <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.quiz.edit', 'id' => $lesson->id]) }}', 'Chỉnh sửa Quiz')" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                                <i class="fi-rr-pencil"></i>
                                            </button>
                                        @else
                                            <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.course.lesson_edit', 'id' => $lesson->id]) }}', 'Chỉnh sửa bài học')" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                                <i class="fi-rr-pencil"></i>
                                            </button>
                                        @endif
                                        <button type="button" onclick="confirmModal('{{ route('admin.lesson.delete', $lesson->id) }}')" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Xóa bài học">
                                            <i class="fi-rr-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="empty-lessons">
                                <div class="text-center py-4">
                                    <i class="fi-rr-document text-muted mb-2" style="font-size: 2rem;"></i>
                                    <p class="text-muted mb-0">Chưa có bài học nào trong section này</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @empty
                <!-- Empty State -->
                <div class="empty-curriculum">
                    <div class="text-center py-5">
                        <i class="fi-rr-add text-muted mb-3" style="font-size: 3rem;"></i>
                        <h4 class="text-muted mb-3">Chưa có section nào</h4>
                        <p class="text-muted mb-4">Hãy tạo section đầu tiên để bắt đầu xây dựng khóa học</p>
                        <button type="button" onclick="ajaxModal('{{ route('modal', ['admin.course.create_section', 'id' => $course_details->id]) }}', 'Thêm section mới')" class="btn btn-primary">
                            <i class="fi-rr-add me-1"></i> Tạo Section Đầu Tiên
                        </button>
                    </div>
                </div>
            @endforelse
        </div>
    </div>
</div>

@push('css')
    <!-- SortableJS CSS -->
    <link rel="stylesheet" href="{{ asset('assets/css/curriculum-builder.css') }}">

    <!-- Inline CSS for immediate styling -->
    <style>
        .curriculum-builder {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .curriculum-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 20px;
            color: white !important;
            margin-bottom: 24px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .curriculum-header * {
            color: white !important;
        }

        .curriculum-header small {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .curriculum-header .btn {
            background: rgba(255, 255, 255, 0.2) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            color: white !important;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .curriculum-header .btn:hover {
            background: rgba(255, 255, 255, 0.3) !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .curriculum-header .btn i {
            color: white !important;
        }

        .section-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .section-header {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
        }

        .section-drag-handle, .lesson-drag-handle {
            margin-right: 12px;
            cursor: grab;
            padding: 8px;
            border-radius: 6px;
            transition: background-color 0.2s ease;
        }

        .section-drag-handle:hover, .lesson-drag-handle:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .section-drag-handle:active, .lesson-drag-handle:active {
            cursor: grabbing;
        }

        .lesson-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            background: white;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.3s ease;
        }

        .lesson-item:hover {
            background: #f8fafc;
            transform: translateX(4px);
        }

        .lesson-icon {
            margin-right: 12px;
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        .lesson-content {
            flex: 1;
        }

        .lesson-actions {
            display: flex;
            gap: 6px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .lesson-item:hover .lesson-actions {
            opacity: 1;
        }

        .sortable-ghost {
            opacity: 0.5;
        }

        .sortable-chosen {
            background: #eff6ff !important;
            box-shadow: inset 3px 0 0 #667eea;
        }

        .badge {
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .badge-primary { background: #dbeafe; color: #1e40af; }
        .badge-warning { background: #fef3c7; color: #92400e; }
        .badge-info { background: #dbeafe; color: #1e40af; }
        .badge-success { background: #d1fae5; color: #065f46; }

        /* Section toggle animations */
        .section-toggle {
            transition: all 0.3s ease;
        }

        .section-toggle.collapsed {
            transform: rotate(-90deg);
        }

        .section-lessons {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .section-lessons.expanding {
            animation: slideDown 0.3s ease-out;
        }

        .section-lessons.collapsing {
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                max-height: 1000px;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 1;
                max-height: 1000px;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                max-height: 0;
                transform: translateY(-10px);
            }
        }
    </style>
@endpush

@push('js')
    <!-- SortableJS Library -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <!-- Disable the class-based JS to avoid conflicts -->
    <!-- <script src="{{ asset('assets/js/curriculum-sortable.js') }}"></script> -->

    <!-- Direct implementation script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing sortable...');

            // Check if SortableJS is available
            if (typeof Sortable === 'undefined') {
                console.error('SortableJS not loaded!');
                return;
            }

            const container = document.getElementById('curriculum-container');
            if (!container) {
                console.error('Curriculum container not found!');
                return;
            }

            console.log('Container found, initializing...');

            // Debug: Check for required elements
            const sectionCards = container.querySelectorAll('.section-card');
            const lessonContainers = container.querySelectorAll('.section-lessons');
            const lessonItems = container.querySelectorAll('.lesson-item');
            const csrfToken = document.querySelector('meta[name="csrf_token"]');

            console.log('Debug info:');
            console.log('- Section cards:', sectionCards.length);
            console.log('- Lesson containers:', lessonContainers.length);
            console.log('- Lesson items:', lessonItems.length);
            console.log('- CSRF token found:', !!csrfToken);
            console.log('- CSRF token value:', csrfToken ? csrfToken.content : 'null');
            console.log('- Course ID:', container.dataset.courseId);

            // Ensure we have the required elements
            if (sectionCards.length === 0) {
                console.warn('No section cards found');
            }
            if (lessonContainers.length === 0) {
                console.warn('No lesson containers found');
            }

            // Initialize section sortable
            console.log('Initializing section sortable...');
            var sectionSortable = new Sortable(container, {
                animation: 350,
                easing: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                handle: '.section-drag-handle',
                onStart: function(evt) {
                    console.log('Section drag started');
                    if (evt.item) {
                        evt.item.style.transform = 'rotate(2deg)';
                    }
                },
                onEnd: function(evt) {
                    try {
                        console.log('Section drag ended');
                        if (evt.item) {
                            evt.item.style.transform = '';
                        }
                        updateSectionNumbers();
                        saveSectionOrder();
                        showNotification('Đã cập nhật thứ tự sections', 'success');
                    } catch (error) {
                        console.error('Error in section drag end handler:', error);
                        showNotification('Có lỗi xảy ra khi di chuyển section', 'error');
                    }
                }
            });
            console.log('Section sortable initialized:', !!sectionSortable);

            // Initialize lesson sortables
            console.log('Initializing lesson sortables...');
            console.log('Using lesson containers from debug section:', lessonContainers.length);

            lessonContainers.forEach(function(lessonContainer, index) {
                console.log('Initializing sortable for container', index, lessonContainer);

                var lessonSortable = new Sortable(lessonContainer, {
                    group: 'lessons',
                    animation: 350,
                    easing: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    handle: '.lesson-drag-handle',
                    onStart: function(evt) {
                        console.log('Lesson drag started');

                        // Store original section for comparison
                        if (evt.item && evt.from) {
                            evt.item.setAttribute('data-original-section', evt.from.getAttribute('data-section-id') || '');
                        }

                        // Visual feedback
                        if (evt.item) {
                            evt.item.style.transform = 'rotate(1deg) scale(1.02)';
                            evt.item.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                        }
                    },
                    onEnd: function(evt) {
                        console.log('Lesson drag end event triggered');

                        // Reset visual styles safely
                        if (evt.item) {
                            evt.item.style.transform = '';
                            evt.item.style.boxShadow = '';
                        }

                        // Get lesson and section info safely
                        var lessonId = null;
                        var newSectionId = null;
                        var originalSectionId = null;

                        if (evt.item && evt.item.dataset) {
                            lessonId = evt.item.dataset.lessonId;
                            originalSectionId = evt.item.getAttribute('data-original-section');
                        }

                        if (evt.to && evt.to.dataset) {
                            newSectionId = evt.to.dataset.sectionId;
                        }

                        console.log('Lesson ID:', lessonId);
                        console.log('New Section ID:', newSectionId);
                        console.log('Original Section ID:', originalSectionId);

                        // Validate required data
                        if (!lessonId || !newSectionId) {
                            console.error('Missing lesson ID or section ID');
                            showNotification('Có lỗi: thiếu thông tin bài học hoặc section', 'error');
                            return;
                        }

                        // Update lesson counts
                        updateLessonCounts();

                        // Determine if lesson moved to different section
                        if (originalSectionId && originalSectionId !== newSectionId) {
                            // Moved to different section
                            console.log('Moving lesson to different section');
                            updateLessonSection(lessonId, newSectionId, evt.to);
                        } else {
                            // Moved within same section or no original section data
                            console.log('Reordering lesson within section');
                            saveLessonOrder(newSectionId, evt.to);
                        }

                        // Clean up
                        if (evt.item) {
                            evt.item.removeAttribute('data-original-section');
                        }
                    },
                    onMove: function(evt) {
                        // Enhanced visual feedback when hovering over drop zones
                        const related = evt.related;
                        if (related && related.classList.contains('section-lessons')) {
                            // Remove previous hover effects
                            document.querySelectorAll('.section-lessons').forEach(function(section) {
                                section.classList.remove('drag-over');
                            });

                            // Add hover effect to current target
                            related.classList.add('drag-over');
                        }
                        return true; // Allow the move
                    }
                });
                console.log('Lesson sortable', index, 'initialized:', !!lessonSortable);
            });

            // Helper functions
            function updateSectionNumbers() {
                try {
                    if (!container) return;

                    const sectionCards = container.querySelectorAll('.section-card');
                    sectionCards.forEach(function(card, index) {
                        if (!card) return;

                        const numberSpan = card.querySelector('.section-number');
                        if (numberSpan) {
                            numberSpan.textContent = (index + 1) + '.';
                        }
                    });
                } catch (error) {
                    console.error('Error updating section numbers:', error);
                }
            }

            function updateLessonCounts() {
                const sectionCards = document.querySelectorAll('.section-card');
                sectionCards.forEach(function(card) {
                    if (!card) return;

                    const lessonsContainer = card.querySelector('.section-lessons');
                    if (!lessonsContainer) return;

                    const lessonItems = lessonsContainer.querySelectorAll('.lesson-item');
                    const countElement = card.querySelector('.section-info small');

                    if (countElement) {
                        countElement.textContent = lessonItems.length + ' bài học';
                    }
                });
            }

            function saveSectionOrder() {
                if (!container) return;

                const sectionCards = container.querySelectorAll('.section-card');
                const sectionIds = [];

                sectionCards.forEach(function(card) {
                    if (card && card.dataset && card.dataset.sectionId) {
                        sectionIds.push(card.dataset.sectionId);
                    }
                });

                const csrfToken = document.querySelector('meta[name="csrf_token"]');
                if (!csrfToken) {
                    console.error('CSRF token not found');
                    return;
                }

                const formData = new FormData();
                formData.append('itemJSON', JSON.stringify(sectionIds));
                formData.append('course_id', container.dataset.courseId);
                formData.append('_token', csrfToken.content);

                fetch('/admin/section/sort', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Sections order saved');
                })
                .catch(error => {
                    console.error('Error saving sections order:', error);
                });
            }

            function saveLessonOrder(sectionId, lessonContainer) {
                console.log('saveLessonOrder called with:', sectionId, lessonContainer);

                if (!lessonContainer || !sectionId) {
                    console.error('Invalid parameters for saveLessonOrder');
                    showNotification('Lỗi: thiếu thông tin section', 'error');
                    return;
                }

                try {
                    const lessonItems = lessonContainer.querySelectorAll('.lesson-item');
                    const lessonIds = [];

                    console.log('Found lesson items:', lessonItems.length);

                    lessonItems.forEach(function(item) {
                        if (item && item.dataset && item.dataset.lessonId) {
                            lessonIds.push(item.dataset.lessonId);
                        }
                    });

                    console.log('Lesson IDs to save:', lessonIds);

                    const csrfToken = document.querySelector('meta[name="csrf_token"]');
                    if (!csrfToken) {
                        console.error('CSRF token not found');
                        showNotification('Lỗi: không tìm thấy CSRF token', 'error');
                        return;
                    }

                    const formData = new FormData();
                    formData.append('itemJSON', JSON.stringify(lessonIds));
                    formData.append('section_id', sectionId);
                    formData.append('_token', csrfToken.content);

                    console.log('Sending lesson sort request...');

                    fetch('/admin/lesson/sort', {
                        method: 'POST',
                        body: formData
                    })
                    .then(function(response) {
                        console.log('Response received:', response.status);
                        return response.json();
                    })
                    .then(function(data) {
                        console.log('Response data:', data);
                        if (data.success) {
                            console.log('Lessons order saved successfully');
                            showNotification('Đã cập nhật thứ tự bài học', 'success');
                        } else {
                            console.error('Server returned error:', data);
                            showNotification('Lỗi từ server khi lưu thứ tự bài học', 'error');
                        }
                    })
                    .catch(function(error) {
                        console.error('Error saving lessons order:', error);
                        showNotification('Có lỗi khi lưu thứ tự bài học', 'error');
                    });
                } catch (error) {
                    console.error('Error in saveLessonOrder:', error);
                    showNotification('Có lỗi trong quá trình lưu', 'error');
                }
            }

            function updateLessonSection(lessonId, newSectionId, lessonContainer) {
                console.log('updateLessonSection called with:', lessonId, newSectionId, lessonContainer);

                if (!lessonId || !newSectionId || !lessonContainer) {
                    console.error('Invalid parameters for updateLessonSection');
                    showNotification('Lỗi: thiếu thông tin để di chuyển bài học', 'error');
                    return;
                }

                try {
                    // Get lesson order in new section
                    const lessonItems = lessonContainer.querySelectorAll('.lesson-item');
                    const lessonIds = [];

                    lessonItems.forEach(function(item) {
                        if (item && item.dataset && item.dataset.lessonId) {
                            lessonIds.push(item.dataset.lessonId);
                        }
                    });

                    console.log('Lesson order in new section:', lessonIds);

                    const csrfToken = document.querySelector('meta[name="csrf_token"]');
                    if (!csrfToken) {
                        console.error('CSRF token not found');
                        showNotification('Lỗi: không tìm thấy CSRF token', 'error');
                        return;
                    }

                    const formData = new FormData();
                    formData.append('lesson_id', lessonId);
                    formData.append('section_id', newSectionId);
                    formData.append('lesson_order', JSON.stringify(lessonIds));
                    formData.append('_token', csrfToken.content);

                    console.log('Sending lesson move request...');

                    fetch('/admin/lesson/move-section', {
                        method: 'POST',
                        body: formData
                    })
                    .then(function(response) {
                        console.log('Move response received:', response.status);
                        return response.json();
                    })
                    .then(function(data) {
                        console.log('Move response data:', data);
                        if (data.success) {
                            console.log('Lesson moved successfully');
                            showNotification('Đã di chuyển bài học thành công', 'success');
                        } else {
                            console.error('Error moving lesson:', data);
                            showNotification('Có lỗi khi di chuyển bài học', 'error');
                        }
                    })
                    .catch(function(error) {
                        console.error('Error moving lesson:', error);
                        showNotification('Có lỗi khi di chuyển bài học', 'error');
                    });
                } catch (error) {
                    console.error('Error in updateLessonSection:', error);
                    showNotification('Có lỗi trong quá trình di chuyển', 'error');
                }
            }

            function showNotification(message, type = 'info') {
                // Remove existing notifications
                const existingNotifications = document.querySelectorAll('.curriculum-notification');
                existingNotifications.forEach(function(notification) {
                    notification.remove();
                });

                // Create notification element
                const notification = document.createElement('div');
                notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed curriculum-notification`;
                notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
                notification.innerHTML = `
                    <i class="fi-rr-${type === 'success' ? 'check' : 'cross'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(notification);

                // Auto remove after 3 seconds
                setTimeout(function() {
                    if (notification.parentNode) {
                        notification.classList.remove('show');
                        setTimeout(function() {
                            if (notification.parentNode) {
                                notification.remove();
                            }
                        }, 150);
                    }
                }, 3000);
            }

            // Initialize section toggle functionality
            initSectionToggles();

            console.log('All sortables initialized successfully');
            console.log('Total lesson containers processed:', lessonContainers.length);
        });

        // Section toggle functionality
        function initSectionToggles() {
            console.log('Initializing section toggles...');

            document.addEventListener('click', function(e) {
                if (e.target.closest('.section-toggle')) {
                    e.preventDefault();
                    e.stopPropagation();

                    const toggle = e.target.closest('.section-toggle');
                    const sectionCard = toggle.closest('.section-card');
                    const lessonsContainer = sectionCard.querySelector('.section-lessons');
                    const icon = toggle.querySelector('i');

                    console.log('Section toggle clicked', {
                        toggle: toggle,
                        sectionCard: sectionCard,
                        lessonsContainer: lessonsContainer,
                        icon: icon
                    });

                    toggleSection(toggle, lessonsContainer, icon);
                }
            });

            console.log('Section toggles initialized');
        }

        function toggleSection(toggle, lessonsContainer, icon) {
            if (!lessonsContainer || !icon) {
                console.error('Missing elements for toggle');
                return;
            }

            const isCollapsed = lessonsContainer.style.display === 'none';

            console.log('Toggling section, currently collapsed:', isCollapsed);

            if (isCollapsed) {
                // Expand
                lessonsContainer.style.display = 'block';
                lessonsContainer.classList.add('expanding');
                icon.classList.remove('fi-rr-angle-down');
                icon.classList.add('fi-rr-angle-up');
                toggle.classList.remove('collapsed');

                setTimeout(function() {
                    lessonsContainer.classList.remove('expanding');
                }, 300);

                console.log('Section expanded');
            } else {
                // Collapse
                lessonsContainer.classList.add('collapsing');
                icon.classList.remove('fi-rr-angle-up');
                icon.classList.add('fi-rr-angle-down');
                toggle.classList.add('collapsed');

                setTimeout(function() {
                    lessonsContainer.style.display = 'none';
                    lessonsContainer.classList.remove('collapsing');
                }, 300);

                console.log('Section collapsed');
            }
        }
    </script>
@endpush
