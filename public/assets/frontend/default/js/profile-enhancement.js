/**
 * Enhanced Profile Page JavaScript
 * Provides advanced interactions and validations for the profile page
 */

$(document).ready(function() {
    
    // Form validation
    function validateForm() {
        let isValid = true;
        
        // Name validation
        const name = $('#name').val().trim();
        if (name.length < 2) {
            showFieldError('#name', 'Tên phải có ít nhất 2 ký tự');
            isValid = false;
        } else {
            showFieldSuccess('#name');
        }
        
        // Email validation
        const email = $('#email').val().trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showFieldError('#email', 'Email không hợp lệ');
            isValid = false;
        } else {
            showFieldSuccess('#email');
        }
        
        // Phone validation
        const phone = $('#phone').val().trim();
        const phoneRegex = /^[0-9]{10,11}$/;
        if (phone && !phoneRegex.test(phone)) {
            showFieldError('#phone', 'Số điện thoại phải có 10-11 chữ số');
            isValid = false;
        } else if (phone) {
            showFieldSuccess('#phone');
        }
        
        // Password validation
        const oldPassword = $('#old_password').val();
        const newPassword = $('#new_password').val();
        
        if (oldPassword && !newPassword) {
            showFieldError('#new_password', 'Vui lòng nhập mật khẩu mới');
            isValid = false;
        } else if (!oldPassword && newPassword) {
            showFieldError('#old_password', 'Vui lòng nhập mật khẩu cũ');
            isValid = false;
        } else if (newPassword && newPassword.length < 6) {
            showFieldError('#new_password', 'Mật khẩu mới phải có ít nhất 6 ký tự');
            isValid = false;
        } else if (newPassword) {
            showFieldSuccess('#new_password');
            showFieldSuccess('#old_password');
        }
        
        return isValid;
    }
    
    // Show field error
    function showFieldError(fieldId, message) {
        const field = $(fieldId);
        field.removeClass('is-valid').addClass('is-invalid');
        
        // Remove existing error message
        field.siblings('.invalid-feedback').remove();
        
        // Add error message
        field.after(`<div class="invalid-feedback">${message}</div>`);
    }
    
    // Show field success
    function showFieldSuccess(fieldId) {
        const field = $(fieldId);
        field.removeClass('is-invalid').addClass('is-valid');
        field.siblings('.invalid-feedback').remove();
    }
    
    // Clear field validation
    function clearFieldValidation(fieldId) {
        const field = $(fieldId);
        field.removeClass('is-valid is-invalid');
        field.siblings('.invalid-feedback').remove();
    }
    
    // Real-time validation
    $('#name, #email, #phone, #old_password, #new_password').on('blur', function() {
        validateForm();
    });
    
    // Clear validation on focus
    $('#name, #email, #phone, #old_password, #new_password').on('focus', function() {
        clearFieldValidation('#' + this.id);
    });
    
    // Form submission with enhanced validation
    $('form').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            
            // Scroll to first error
            const firstError = $('.is-invalid').first();
            if (firstError.length) {
                $('html, body').animate({
                    scrollTop: firstError.offset().top - 100
                }, 500);
            }
            
            // Show error notification
            showNotification('Vui lòng kiểm tra lại thông tin đã nhập', 'error');
            return false;
        }
        
        // Show success notification
        showNotification('Đang lưu thông tin...', 'info');
    });
    
    // Show notification
    function showNotification(message, type = 'info') {
        // Remove existing notifications
        $('.notification').remove();
        
        const notificationClass = type === 'error' ? 'alert-danger' : 
                                 type === 'success' ? 'alert-success' : 'alert-info';
        
        const notification = $(`
            <div class="notification alert ${notificationClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `);
        
        $('body').append(notification);
        
        // Auto remove after 5 seconds
        setTimeout(function() {
            notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }
    
    // Enhanced input interactions
    $('.form-control').each(function() {
        const $this = $(this);
        const placeholder = $this.attr('placeholder');
        
        // Add floating label effect
        $this.on('focus blur', function() {
            const $label = $this.siblings('.form-label');
            if ($this.val() || $this.is(':focus')) {
                $label.addClass('active');
            } else {
                $label.removeClass('active');
            }
        });
        
        // Trigger on page load for pre-filled fields
        if ($this.val()) {
            $this.siblings('.form-label').addClass('active');
        }
    });
    
    // Bank account number formatting
    $('#bank_account_number').on('input', function() {
        let value = $(this).val().replace(/\D/g, ''); // Remove non-digits
        
        // Format with spaces every 4 digits
        value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
        
        $(this).val(value);
    });
    
    // Auto-capitalize bank account name
    $('#bank_account_name').on('input', function() {
        const value = $(this).val();
        $(this).val(value.toUpperCase());
    });
    
    // Enhanced Select2 styling
    $('#bank_select').on('select2:open', function() {
        $('.select2-dropdown').addClass('enhanced-dropdown');
    });
    
    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl + S to save
        if (e.ctrlKey && e.which === 83) {
            e.preventDefault();
            $('form').submit();
        }
        
        // Escape to clear focus
        if (e.which === 27) {
            $('.form-control:focus').blur();
        }
    });
    
    // Add tooltips for better UX
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Progressive enhancement for older browsers
    if (!CSS.supports('display', 'grid')) {
        $('.profile-card, .bank-section').css({
            'box-shadow': '0 2px 10px rgba(0,0,0,0.1)',
            'border': '1px solid #ddd'
        });
    }
    
    // Accessibility improvements
    $('.form-control').on('invalid', function() {
        $(this).attr('aria-describedby', $(this).attr('id') + '-error');
    });
    
    // Auto-save draft (optional feature)
    let autoSaveTimer;
    $('.form-control').on('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            // Save form data to localStorage
            const formData = {};
            $('.form-control').each(function() {
                if ($(this).attr('type') !== 'password') {
                    formData[$(this).attr('name')] = $(this).val();
                }
            });
            localStorage.setItem('profileFormDraft', JSON.stringify(formData));
        }, 2000);
    });
    
    // Load draft on page load
    const savedDraft = localStorage.getItem('profileFormDraft');
    if (savedDraft) {
        try {
            const formData = JSON.parse(savedDraft);
            Object.keys(formData).forEach(function(key) {
                $(`[name="${key}"]`).val(formData[key]);
            });
        } catch (e) {
            console.log('Could not load saved draft');
        }
    }
    
    // Clear draft on successful submission
    $('form').on('submit', function() {
        localStorage.removeItem('profileFormDraft');
    });
    
});
