{"version": 3, "file": "lang/summernote-de-CH.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,MADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,eAHP;AAIJC,QAAAA,KAAK,EAAE,cAJH;AAKJC,QAAAA,MAAM,EAAE,YALJ;AAMJC,QAAAA,IAAI,EAAE,YANF;AAOJC,QAAAA,aAAa,EAAE,iBAPX;AAQJC,QAAAA,SAAS,EAAE,cARP;AASJC,QAAAA,WAAW,EAAE,cATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,MAAM,EAAE,eAFH;AAGLC,QAAAA,UAAU,EAAE,gBAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,aAAa,EAAE,YALV;AAMLC,QAAAA,SAAS,EAAE,aANN;AAOLC,QAAAA,UAAU,EAAE,cAPP;AAQLC,QAAAA,SAAS,EAAE,gBARN;AASLC,QAAAA,YAAY,EAAE,mBATT;AAULC,QAAAA,WAAW,EAAE,aAVR;AAWLC,QAAAA,cAAc,EAAE,gBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,qBAbV;AAcLC,QAAAA,SAAS,EAAE,uBAdN;AAeLC,QAAAA,eAAe,EAAE,iBAfZ;AAgBLC,QAAAA,eAAe,EAAE,sBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,oCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,UAlBA;AAmBLC,QAAAA,MAAM,EAAE,gBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,WAFN;AAGLpB,QAAAA,MAAM,EAAE,gBAHH;AAILgB,QAAAA,GAAG,EAAE,WAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,eAFJ;AAGJuB,QAAAA,MAAM,EAAE,gBAHJ;AAIJC,QAAAA,IAAI,EAAE,YAJF;AAKJC,QAAAA,aAAa,EAAE,aALX;AAMJT,QAAAA,GAAG,EAAE,UAND;AAOJU,QAAAA,eAAe,EAAE,yBAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OA1CC;AAoDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,kBAFR;AAGLC,QAAAA,WAAW,EAAE,mBAHR;AAILC,QAAAA,UAAU,EAAE,gBAJP;AAKLC,QAAAA,WAAW,EAAE,iBALR;AAMLC,QAAAA,MAAM,EAAE,eANH;AAOLC,QAAAA,MAAM,EAAE,gBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OApDA;AA8DPC,MAAAA,EAAE,EAAE;AACFpC,QAAAA,MAAM,EAAE;AADN,OA9DG;AAiEPqC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,MAAM,EAAE,QAFH;AAGLC,QAAAA,CAAC,EAAE,QAHE;AAILC,QAAAA,UAAU,EAAE,OAJP;AAKLC,QAAAA,GAAG,EAAE,WALA;AAMLC,QAAAA,EAAE,EAAE,eANC;AAOLC,QAAAA,EAAE,EAAE,eAPC;AAQLC,QAAAA,EAAE,EAAE,eARC;AASLC,QAAAA,EAAE,EAAE,eATC;AAULC,QAAAA,EAAE,EAAE,eAVC;AAWLC,QAAAA,EAAE,EAAE;AAXC,OAjEA;AA8EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,YADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA9EA;AAkFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,UAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAlFF;AAuFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,QADF;AAETC,QAAAA,OAAO,EAAE,oBAFA;AAGTC,QAAAA,MAAM,EAAE,oBAHC;AAITC,QAAAA,IAAI,EAAE,kBAJG;AAKTC,QAAAA,MAAM,EAAE,sBALC;AAMTC,QAAAA,KAAK,EAAE,mBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAvFJ;AAgGPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,cADH;AAELC,QAAAA,IAAI,EAAE,gBAFD;AAGLC,QAAAA,UAAU,EAAE,kBAHP;AAILC,QAAAA,UAAU,EAAE,cAJP;AAKLC,QAAAA,WAAW,EAAE,aALR;AAMLC,QAAAA,cAAc,EAAE,oBANX;AAOLC,QAAAA,KAAK,EAAE,cAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OAhGA;AA0GPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,cADH;AAERC,QAAAA,KAAK,EAAE,YAFC;AAGRC,QAAAA,cAAc,EAAE,kBAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,oBALb;AAMRC,QAAAA,aAAa,EAAE,gBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OA1GH;AAmHP1B,MAAAA,IAAI,EAAE;AACJ2B,QAAAA,eAAe,EAAE,iBADb;AAEJC,QAAAA,IAAI,EAAE,6BAFF;AAGJC,QAAAA,IAAI,EAAE,8BAHF;AAIJC,QAAAA,GAAG,EAAE,mBAJD;AAKJC,QAAAA,KAAK,EAAE,kBALH;AAMJ9F,QAAAA,IAAI,EAAE,cANF;AAOJC,QAAAA,MAAM,EAAE,gBAPJ;AAQJC,QAAAA,SAAS,EAAE,gBARP;AASJI,QAAAA,aAAa,EAAE,gBATX;AAUJyF,QAAAA,YAAY,EAAE,iBAVV;AAWJC,QAAAA,WAAW,EAAE,aAXT;AAYJC,QAAAA,aAAa,EAAE,QAZX;AAaJC,QAAAA,YAAY,EAAE,cAbV;AAcJC,QAAAA,WAAW,EAAE,WAdT;AAeJC,QAAAA,mBAAmB,EAAE,qBAfjB;AAgBJC,QAAAA,iBAAiB,EAAE,mBAhBf;AAiBJlC,QAAAA,OAAO,EAAE,4BAjBL;AAkBJC,QAAAA,MAAM,EAAE,4BAlBJ;AAmBJkC,QAAAA,UAAU,EAAE,+CAnBR;AAoBJC,QAAAA,QAAQ,EAAE,mCApBN;AAqBJC,QAAAA,QAAQ,EAAE,mCArBN;AAsBJC,QAAAA,QAAQ,EAAE,mCAtBN;AAuBJC,QAAAA,QAAQ,EAAE,mCAvBN;AAwBJC,QAAAA,QAAQ,EAAE,mCAxBN;AAyBJC,QAAAA,QAAQ,EAAE,mCAzBN;AA0BJC,QAAAA,oBAAoB,EAAE,iCA1BlB;AA2BJ,2BAAmB;AA3Bf,OAnHC;AAgJPC,MAAAA,OAAO,EAAE;AACPnB,QAAAA,IAAI,EAAE,YADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OAhJF;AAoJPmB,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,eADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AApJN;AADiB,GAA5B;AA2JD,CA5JD,EA4JGC,MA5JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-de-CH.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'de-CH': {\n      font: {\n        bold: '<PERSON><PERSON>',\n        italic: 'Kurs<PERSON>',\n        underline: '<PERSON>ters<PERSON><PERSON>',\n        clear: '<PERSON>ur<PERSON><PERSON><PERSON>',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: '<PERSON>hrift<PERSON>',\n        strikethrough: 'Durchgestric<PERSON>',\n        subscript: 'Tiefgestellt',\n        superscript: 'Hochgestellt',\n        size: 'Schriftgrösse',\n      },\n      image: {\n        image: 'Bild',\n        insert: 'Bild einfügen',\n        resizeFull: 'Originalgrösse',\n        resizeHalf: '1/2 Grösse',\n        resizeQuarter: '1/4 Grösse',\n        floatLeft: 'Linksbündig',\n        floatRight: 'Rechtsbündig',\n        floatNone: 'Kein Textfluss',\n        shapeRounded: 'Abgerundete Ecken',\n        shapeCircle: 'Kreisförmig',\n        shapeThumbnail: '\"Vorschaubild\"',\n        shapeNone: '<PERSON><PERSON>',\n        dragImageHere: 'Bild hierher ziehen',\n        dropImage: 'Bild oder Text nehmen',\n        selectFromFiles: 'Datei auswählen',\n        maximumFileSize: 'Maximale Dateigrösse',\n        maximumFileSizeError: 'Maximale Dateigrösse überschritten',\n        url: 'Bild URL',\n        remove: 'Bild entfernen',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Videolink',\n        insert: 'Video einfügen',\n        url: 'Video URL',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion oder Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Link einfügen',\n        unlink: 'Link entfernen',\n        edit: 'Bearbeiten',\n        textToDisplay: 'Anzeigetext',\n        url: 'Link URL',\n        openInNewWindow: 'In neuem Fenster öffnen',\n        useProtocol: 'Standardprotokoll verwenden',\n      },\n      table: {\n        table: 'Tabelle',\n        addRowAbove: '+ Zeile oberhalb',\n        addRowBelow: '+ Zeile unterhalb',\n        addColLeft: '+ Spalte links',\n        addColRight: '+ Spalte rechts',\n        delRow: 'Zeile löschen',\n        delCol: 'Spalte löschen',\n        delTable: 'Tabelle löschen',\n      },\n      hr: {\n        insert: 'Horizontale Linie einfügen',\n      },\n      style: {\n        style: 'Stil',\n        normal: 'Normal',\n        p: 'Normal',\n        blockquote: 'Zitat',\n        pre: 'Quellcode',\n        h1: 'Überschrift 1',\n        h2: 'Überschrift 2',\n        h3: 'Überschrift 3',\n        h4: 'Überschrift 4',\n        h5: 'Überschrift 5',\n        h6: 'Überschrift 6',\n      },\n      lists: {\n        unordered: 'Aufzählung',\n        ordered: 'Nummerierung',\n      },\n      options: {\n        help: 'Hilfe',\n        fullscreen: 'Vollbild',\n        codeview: 'Quellcode anzeigen',\n      },\n      paragraph: {\n        paragraph: 'Absatz',\n        outdent: 'Einzug verkleinern',\n        indent: 'Einzug vergrössern',\n        left: 'Links ausrichten',\n        center: 'Zentriert ausrichten',\n        right: 'Rechts ausrichten',\n        justify: 'Blocksatz',\n      },\n      color: {\n        recent: 'Letzte Farbe',\n        more: 'Weitere Farben',\n        background: 'Hintergrundfarbe',\n        foreground: 'Schriftfarbe',\n        transparent: 'Transparenz',\n        setTransparent: 'Transparenz setzen',\n        reset: 'Zurücksetzen',\n        resetToDefault: 'Auf Standard zurücksetzen',\n      },\n      shortcut: {\n        shortcuts: 'Tastenkürzel',\n        close: 'Schliessen',\n        textFormatting: 'Textformatierung',\n        action: 'Aktion',\n        paragraphFormatting: 'Absatzformatierung',\n        documentStyle: 'Dokumentenstil',\n        extraKeys: 'Weitere Tasten',\n      },\n      help: {\n        insertParagraph: 'Absatz einfügen',\n        undo: 'Letzte Anweisung rückgängig',\n        redo: 'Letzte Anweisung wiederholen',\n        tab: 'Einzug hinzufügen',\n        untab: 'Einzug entfernen',\n        bold: 'Schrift Fett',\n        italic: 'Schrift Kursiv',\n        underline: 'Unterstreichen',\n        strikethrough: 'Durchstreichen',\n        removeFormat: 'Entfernt Format',\n        justifyLeft: 'Linksbündig',\n        justifyCenter: 'Mittig',\n        justifyRight: 'Rechtsbündig',\n        justifyFull: 'Blocksatz',\n        insertUnorderedList: 'Unnummerierte Liste',\n        insertOrderedList: 'Nummerierte Liste',\n        outdent: 'Aktuellen Absatz ausrücken',\n        indent: 'Aktuellen Absatz einrücken',\n        formatPara: 'Formatiert aktuellen Block als Absatz (P-Tag)',\n        formatH1: 'Formatiert aktuellen Block als H1',\n        formatH2: 'Formatiert aktuellen Block als H2',\n        formatH3: 'Formatiert aktuellen Block als H3',\n        formatH4: 'Formatiert aktuellen Block als H4',\n        formatH5: 'Formatiert aktuellen Block als H5',\n        formatH6: 'Formatiert aktuellen Block als H6',\n        insertHorizontalRule: 'Fügt eine horizontale Linie ein',\n        'linkDialog.show': 'Zeigt den Linkdialog',\n      },\n      history: {\n        undo: 'Rückgängig',\n        redo: 'Wiederholen',\n      },\n      specialChar: {\n        specialChar: 'Sonderzeichen',\n        select: 'Zeichen auswählen',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "normal", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "insertParagraph", "undo", "redo", "tab", "untab", "removeFormat", "justifyLeft", "justifyCenter", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "formatPara", "formatH1", "formatH2", "formatH3", "formatH4", "formatH5", "formatH6", "insertHorizontalRule", "history", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}