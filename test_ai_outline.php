<?php

// Test script for AI Outline Generator
echo "Testing AI Outline Generator...\n";

// Test the OpenAI API call
function testOpenAiApi() {
    $prompt = "Tạo outline cho khóa học Shopee từ A-Z. Trả về JSON format với sections và lessons.";
    
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => 'https://api.v3.cm/v1/chat/completions',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer sk-MJ40iVOjgpDUTemzF7A41d1aC03b42Ea917f6c39Cf1f955e',
            'Content-Type: application/json'
        ],
        CURLOPT_POSTFIELDS => json_encode([
            'max_tokens' => 1000,
            'model' => 'gpt-4o-mini',
            'temperature' => 0.7,
            'messages' => [
                [
                    'content' => $prompt,
                    'role' => 'user'
                ]
            ],
            'stream' => false
        ]),
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "CURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return false;
    }
    
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    
    if ($httpCode !== 200) {
        echo "API Error - HTTP $httpCode\n";
        echo "Response: $response\n";
        return false;
    }

    $data = json_decode($response, true);
    
    if (!isset($data['choices'][0]['message']['content'])) {
        echo "Invalid response format\n";
        echo "Response: $response\n";
        return false;
    }

    echo "AI Response:\n";
    echo $data['choices'][0]['message']['content'] . "\n";
    
    return true;
}

// Run test
if (testOpenAiApi()) {
    echo "\n✅ AI API test successful!\n";
} else {
    echo "\n❌ AI API test failed!\n";
}
